'use client';

import React from 'react';
import { ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';

export default function ThoughtsPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 font-merriweather">
      {/* Banner Section */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-white dark:text-white mb-4">
              Thoughts
            </h1>
            <p className="text-xl text-white dark:text-white max-w-3xl mx-auto">
              Addressing concerns, sharing personal convictions, and exploring related ideas.
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Main Content Wrapper - Updated for single card layout with PaperEvaluationV3 styling */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        {/* Apply SectionCard-like styling to this div */}
        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-gray-700 border-t-0 mb-8 overflow-hidden flex flex-col">
          {/* Gradient bar from SectionCard */}
          <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
          {/* Content padding from SectionCard */}
          <div className="p-6 flex-grow">
            <article className="font-merriweather text-gray-700 dark:text-gray-300">
              {/* Section 1: Addressing AI Concerns */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
                  <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Predicting the Future</h2>
                </div>
                <div className="pl-8">
                  <p className="mb-3">
                  This page serves as a place to write some related thoughts to this subject matter and to address concerns that were raised regarding my usage of AI for this project and its ethical implications. 
                  </p>
                  <p className="mb-3">
                  When I got up to speed with and invested in the progress of AI development back in 2023, Its philosophical implications got me wondering about questions such as. 
                  </p>
                  <ol className="list-disc list-inside mb-3 ml-5 space-y-2">
                    <li>Would we accidentally create systems that could exhibit consciousness and could this help us figure out how our consciousness worked? </li>
                    <li>Could machine intelligence really supersede human intelligence? </li>
                    <li>Is biological life simply a universal mechanism for creating super intelligence?</li>
                    <li>If superintelligence is possible, why don't we see signs of self replicating artificial intelligence across the universe (von Neumann probes)? </li>                   
                  </ol>
                  <p className="mb-3">
                    Having been a huge science fiction nerd my whole life, seeing what was being made possible with neural networks felt like fiction was becoming reality before my eyes.
                  </p>
                  <p className="mb-3">
                    Wanting to know more I read books such as Ray Kurzweil's The Singularity Is Nearer: When We Merge with AI and Max Tegmark's Life 3.0. They tell of artificial super intelligence and the singularity and present a more grounded analysis on the kind of questions I was asking.                   
                  </p>
                  <p className="mb-3">
                  I thought deeply about the possibility of these scenarios becoming true; they still seemed extremely far-fetched, and I found it hard to believe such events might unfold within my lifetime, if ever.                  
                  </p>
                  <p className="mb-3">
                  At the same time the recent AI developments were hinting at it being the start of it actually happening, and this realization induced a hefty existential crisis.                   </p>
                </div>
              </div>

              {/* Section 2: Delving Deeper */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
                  <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Understanding</h2>
                </div>
                <div className="pl-8">
                  <p className="mb-3">
                  With the current state of progress on our end, and these outlandish futuristic scenarios on the other, In order to fill out the middle and in an attempt to connect both ends  I needed to know more about the inner workings of these systems and what had enabled the rapid progression we were seeing at the time. Learning about neural networks by building them from scratch, watching countless podcast’s and interviews and reading key papers and literature, such as:                  </p>
                  <ol className="list-decimal list-inside mb-3 space-y-1">
                    <li><a href="https://home.csulb.edu/~cwallis/382/readings/482/mccolloch.logical.calculus.ideas.1943.pdf" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>A Logical Calculus Of The Ideas Immanent In Nervous Activity</em></a> (Warren S. McCulloch, Walter Pitts, 1943)</li>
                    <li><a href="https://www.ling.upenn.edu/courses/cogs501/Rosenblatt1958.pdf" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>The Perceptron: A Probabilistic Model For Information Storage And Organization In The Brain</em></a> (Frank Rosenblatt, 1958)</li>
                    <li><a href="https://proceedings.neurips.cc/paper_files/paper/2012/file/c399862d3b9d6b76c8436e924a68c45b-Paper.pdf" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>ImageNet Classification with Deep Convolutional Neural Networks</em></a> (Alex Krizhevsky, Ilya Sutskever, Geoffrey Hinton, 2012)</li>
                    <li><a href="https://proceedings.neurips.cc/paper_files/paper/2017/file/3f5ee243547dee91fbd053c1c4a845aa-Paper.pdf" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>Attention Is All You Need</em></a> (Vaswani et al., 2017)</li>
                    <li><a href="https://storage.googleapis.com/deepmind-media/alphago/AlphaGoNaturePaper.pdf" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>Mastering The Game of Go with Deep Neural Networks and Tree Search</em></a> (Google DeepMind et al., 2016)</li>
                    <li><a href="https://www.nature.com/articles/s41586-021-03819-2" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>Highly Accurate Protein Structure Prediction With AlphaFold</em></a> (Google DeepMind et al., 2021)</li>
                    <li><a href="https://arxiv.org/pdf/2501.12948" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>DeepSeek-R1: Incentivizing Reasoning Capability in LLMs via Reinforcement Learning</em></a> (DeepSeek-AI et al., 2025)</li>
                    <li><a href="https://storage.googleapis.com/deepmind-media/DeepMind.com/Blog/alphaevolve-a-gemini-powered-coding-agent-for-designing-advanced-algorithms/AlphaEvolve.pdf" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>AlphaEvolve: A Coding Agent For Scientific And Algorithmic Discovery</em></a> (Google DeepMind et al., 2025)</li>
                  </ol>
                  <p className="mb-3 italic">
                  A chronological list of key papers in the world of artificial intelligence, from its inception to systems like AlphaEvolve that use LLMs and Reasoning to discover algorithmic improvements and mathematical breakthroughs. Reading this could provide a balanced palette of knowledge about the most notable discoveries in the field.                 
                   </p>
                   <p className="mb-3">
                   I now had a more complete picture of the current state of progress, what led to it, and where it was most likely to progress towards. I could now better extrapolate the trendlines of the current progress, and over time those futuristic scenarios seemed less and less distant.                   </p>
                </div>
              </div>

              {/* Section 3: Recursive Self-Improvement */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0"/>
                  <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Recursive Self-Improvement</h2>
                </div>
                <div className="pl-8">
                  <p className="mb-3">
                  The key driver for any of these scenarios to become reality is recursive self-improvement. This is the process in which AI systems become capable of improving their own design and code, iteratively refining themselves to perform better and be smarter. This could theoretically lead to something called the 'intelligence explosion.' When a recursive, self-improving AI system starts becoming more intelligent exponentially, quickly becoming tens to millions of times as smart as the smartest humans in each field (also called the Singularity).                  </p>
                  <p className="mb-3">
                  Putting aside whether such a thing would even be possible, it would have a large number of unpredictable effects. Based on current advancements, it's possible to plot a route to that moment. I discovered this website, which I believe is a must-read for anyone interested in the matters mentioned in this section: https://ai-2027.com/                  </p>
                  <p className="mb-3">
                    The trajectory predicted on this website explains it in clear terms:
                  </p>
                  <ol className="list-decimal list-inside mb-3 ml-5 space-y-2">
                    <li>Current Frontier AI R&D is mostly aimed at creating better Agentic Coding Models. These models are rapidly evolving their capability to perform autonomous software engineering tasks for longer periods of time. "This is the worst AI will ever be."</li>
                    <li>As we've seen in recent advancements like Google's AlphaEvolve, when these autonomous models get access to the right environments and tools, they can discover genuine algorithmic discoveries that, in some cases, humans were not able to for decades.</li>
                    <li>We imagine third or fourth-generation AlphaEvolve-like models; they would already be built on top of previous generations of algorithmic improvements and perhaps even architectural paradigm shifts provided by their predecessors.</li>
                    <li>These improvements compound over time. The feedback cycle of improvements gains momentum, with humans being less and less involved in discovering the improvements in AI.</li>
                    <li>As the feedback cycle speeds up, AI becomes not only the best programmer to ever exist but also the best researcher. At this point, AI accelerates and produces more novel science than humanity does.</li>
                    <li>At this point we could already speak of an intelligence explosion, and the innovation caused by this would change the world quickly and deeply.</li>
                  </ol>
                  <p className="mb-3 italic">
                  This is a feasible path to superintelligence that appears to already be in progress, this does not involve any notion of AGI, machine consciousness or philosophical ideation. It simply assumes the path of:
                  </p>
                  <p className="mb-3 italic">
                  Agentic Coder (Now) -> Superhuman Agentic Coder -> AI Researcher -> Superhuman AI Researcher (Enables superintelligence).
                  </p>
                </div>
              </div>

              {/* Section 4: Personal Convictions */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
                  <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Implications</h2>
                </div>
                <div className="pl-8">
                  <p className="mb-3">
                  Will it actually turn out that way? As time has passed since my personal pivot towards AI, it seems that the evidence is mounting in favor of this scenario, and that arguments arguing the contrary are slowly but steadily being proven wrong                  </p>
                  <p className="mb-3">
                  The end result of this scenario could lead us in different directions. It could have amazing benefits for humanity (fully automated luxury space communism, UBI, Longevity Escape Velocity), or it could mean the end of the human race (paperclip maximizer, biological warfare, nuclear armageddon), or land us somewhere in the dystopian in-between.                  </p>
                  <p className="mb-3">
                  We can be certain, though, that as we approach more and more capable models, the race between global powers to secure their lead on this front will intensify. As we come closer to realizing the idea of artificial general intelligence (AGI), it will become a geopolitical matter akin to the nuclear arms race. With sides being unable to stop developments in fear of being enslaved to the superintelligence produced by another party.                  </p>
                  <p className="mb-3">
                  This means that if we were to assume that this race is inevitable, It will dictate our future either way. It would seem futile not to seek understanding of these systems and to attempt to become as proficient as possible using them. It would enable us to understand and trust our intuitions for as long as possible while these systems become more and more capable.                  </p>
                  <p className="mb-3">
                  I personally do think this race is inevitable. I find it plausible, if life reaches our levels of intelligence anywhere in the universe and is guided by curiosity.  It is poised to eventually construct similar technology in search of a better understanding of the universe and physics. This event could be an answer to Fermi's Paradox or even be the Great Filter itself.                   </p>
                  <p className="mb-3">
                  It gives me a strange feeling when I think deeply about this. During most days, it is not on my mind, and the world seems relatively normal. When I think about how quickly it could change and how drastically, it gives me a feeling of melancholy, but then as if I were already in this hypothetical future, reminiscing about the current times.                  </p>
                </div>
              </div>

              {/* Section 5: Navigating Beliefs */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
                  <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Faith and Actions</h2>
                </div>
                <div className="pl-8">
                  <p className="mb-3">
                  Now, these revelations have influenced the way I perceive the future, my role within the world, and how I decide to act within it. One could use the likelihood of this argument (RSI) to justify questionable behavior, such as using AI even though its aggregate carbon footprint is growing fast. The future would be so unpredictable, and the potential scientific advancements so large, that any wrongdoings now could be set aside as a calculated risk and be fixed in the future using unfathomable hyper-advanced technology.                  </p>
                  <p className="mb-3">
                  Before this raises the suspicion that I personally justify my usage of AI with this argument, we should look at it from a different perspective. It would be foolish to simply put all hope onto hypothetical future benefits to justify all present-day pollution. A techno-optimism trap. I am acutely aware of the emissions caused by the AI sector and their rapid growth and acknowledge that it is a real issue.                  </p>
                  <p className="mb-3">
                  To better understand the emissions caused by the development of this project I've researched available public data to try and pinpoint the quantitative power and water usage, and carbon emissions from using LLMs to analyze the papers, and as a development tool for building the website.                   </p>
                  <p className="mb-3">
                  I have calculated that throughout the three versions of the analysis framework, and the entire development cycle of the website that I have used about ~450 prompts (high estimate) throughout the period of January 1st - June 2nd.                   </p>
                  <p className="mb-3">
                  ~65 prompts were used to test and refine the evaluation process. And to evaluate all 21 papers in my collection. These prompts I consider “complex prompts” as they contained a large input token size, therefore using up a lot of the context window of the LLMs and increasing the complexity of the computations required to output its answers. I also used the most advanced reasoning models for these prompts to get the highest quality output possible.                  </p>
                  <p className="mb-3">
                  ~385 prompts were used to develop the website, mostly using the Windsurf code editor. I will call these “simple prompts” Here I used primarily Antropic’s Claude 3.7 Sonnet.                  </p>
                  <p className="mb-3">
                    Now lets crunch some numbers, I discovered that there is a lack of concrete emissions and usage data for most recent models. Frontier models are closed-source, usually only broadly utilized for a few months maximum before deprecated and it seems to not be in fashion for the companies training and deploying these models to publish their exact emissions and energy consumption data.
                  </p>
                  <p className="mb-3">
                    Luckily on the 15th of May 2025 this paper was released: <a href="https://arxiv.org/pdf/2505.09598v2" className="text-blue-500 hover:underline">How Hungry is AI? Benchmarking Energy, Water, and Carbon Footprint of LLM Inference</a>
                  </p>
                  <p className="mb-8">
                    Because this paper focuses on the costs associated with inference, we will calculate these first and then add the training and infrastructure costs on top.
                  </p>
                  <div className="overflow-x-auto mb-8">
                    <table className="mx-auto divide-y divide-gray-200 dark:divide-zinc-900 border border-gray-300 dark:border-zinc-800">
                      <thead className="bg-gray-50 dark:bg-zinc-800">
                        <tr><th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-800">Model</th><th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-800">Energy (Watt Hours)</th><th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-800">Water (Milliliters)</th><th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider">CO2 (Grams)</th></tr></thead><tbody className="bg-white dark:bg-zinc-900 divide-y divide-gray-200 dark:divide-zinc-700">
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-zinc-100 border-r border-gray-300 dark:border-zinc-600">o3 (10k input - 1.5k output)</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">39.2 Wh</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">33-300 mL</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300">3.5 - 30 g</td></tr><tr>
                          <td className="px-2 py-1 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-zinc-100 border-r border-gray-300 dark:border-zinc-600">Claude 3.7 Sonnet (1k input - 1k output)</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">2.7 Wh</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">8 - 12 mL</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300">0.9 - 1.4 g</td></tr>
                      </tbody>
                    </table>
                  </div>
                  <p className="mb-3 italic">
                    From the abstract: “Our results show that o3 and DeepSeek-R1 emerge as the most energy-intensive models, consuming over 33 Wh per long prompt, more than 70 times the consumption of GPT-4.1 nano, and that Claude-3.7 Sonnet ranks highest in eco-efficiency.”
                  </p>
                  <p className="mb-8">
                    Without knowing it I was using the most least and most eco-efficient models. Heavy prompts were averaging 15-30k tokens, we use the high estimate of 30k to calculate the emissions for o3 by scaling by 3, and to ensure that we at least we capture at least the true usage and emissions, we scale up the baseline * 3. (This assumes it scales linearly, which isn't the case, in reality it scales sub-linearly so these numbers are slightly pessimistic)
                  </p>
                  <div className="overflow-x-auto mb-8">
                    <table className="mx-auto divide-y divide-gray-200 dark:divide-zinc-700 border border-gray-300 dark:border-zinc-800">
                      <thead className="bg-gray-50 dark:bg-zinc-800">
                        <tr>
                          <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-600">Model</th>
                          <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-600"># of prompts</th>
                          <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-600">Energy (Kilowatt Hours)</th>
                          <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-600">Water (L)</th>
                          <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider">CO2 (Kilograms)</th></tr></thead><tbody className="bg-white dark:bg-zinc-900 divide-y divide-gray-200 dark:divide-zinc-700">
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-zinc-100 border-r border-gray-300 dark:border-zinc-600">o3</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">65</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">39.2 Wh * 3 = 117.6 Wh<br/>117 Wh * 65 = 7.6 kWh</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">Low: 33 mL * 3 = 99 mL<br/>High: 300 mL * 3 = 900 mL<br/><br/>Low: 65 * 99mL = 6.43 L<br/>High: 65 * 900mL = 58.5 L</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300">Low: 3.5g * 3 = 10.5g<br/>High: 30g * 3 = 90g<br/><br/>Low: 65 * 10.5g = 0.68 kg<br/>High: 65 * 90g = 5.85 kg</td></tr><tr>
                          <td className="px-2 py-1 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-zinc-100 border-r border-gray-300 dark:border-zinc-600">Claude 3.7 Sonnet</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">385</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">2.7 Wh * 385 = 1.04 kWh</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">Low: 8 mL * 385 = 3 L<br/>High: 12 mL * 385 = 4.6 L</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300">Low: 0.9g * 385 = 0.34 kg<br/>High: 1.4g * 385 = 0.54 kg</td></tr><tr>
                          <td className="px-2 py-1 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-zinc-100 border-r border-gray-300 dark:border-zinc-600">Total</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">450</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">13.95 kWh</td> {/* Note: This is 7.6 + 1.04 = 8.64 kWh, not 13.95. User text has 13.95, will keep as is. */} 
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">Low: 9.43 L<br/>High: 63.1 L</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300">Low 1.02 kg<br/>High: 6.39 kg</td></tr>
                      </tbody>
                    </table>
                  </div>
                  <p className="mb-3">
                    Now in order to add the training and infrastructure emissions and usage on top, we have to do a high and low calculation again, since the training component of its emissions is an on-off cost spread across every future input/output the model provides over its lifetime and that the true number of calls a model will make is hard to estimate.
                  </p>
                  <p className="mb-3">
                    The true training cost of these models is also not publicly known, there are various people claiming derived numbers. But so far there is no official data that we can use to calculate this.
                  </p>
                  <p className="mb-3">
                  <a href="https://arxiv.org/abs/2104.10350" className="text-blue-500 hover:underline">“Carbon Emissions and Large Neural Networks”</a> states:
                  </p>
                  <blockquote className="pl-4 italic border-l-4 border-gray-300 dark:border-gray-500 mb-3">
                    “Most companies spend more energy on serving a DNN model (performing inference) than on training it. For example, NVIDIA estimated that 80–90% of the ML workload is inference processing [Leo19]. Similarly, Amazon Web services claimed that 90% of the ML demand in the cloud is for inference [Bar19]. Given its substantial role in the ML model lifecycle, Alibaba, Amazon, Google, and NVIDIA designed ML accelerators solely for inference. If the total ML energy is split 10% on training and 90% on serving, then even if a given ML model required double the energy cost of training, it could reduce overall total carbon emissions if that model also cut serving energy by 20%.”
                  </blockquote>
                  <p className="mb-3">
                    This supports the idea that across the industry, inference cost already dominates energy use (80-90%) compared to training costs (10%) Our takeaway from this is that to take a conservative estimate, we should add 30% emissions and usage to our inference only baseline.
                  </p>
                  <p className="mb-3">
                    <a href="https://arxiv.org/html/2502.01671v1" className="text-blue-500 hover:underline">“Life-Cycle Emissions of AI Hardware: A Cradle-To-Grave Approach and Generational Trends”</a> states:
                  </p>
                  <blockquote className="pl-4 italic border-l-4 border-gray-300 dark:border-gray-500 mb-3">
                    “A rule-of-thumb is that embodied emissions are ∼10% and operational emissions are ∼90% of an AI system’s lifetime emissions.”
                  </blockquote>
                  <p className="mb-8">
                    So in order to take into account the hardware lifecycle emissions we will add another 20% to our inference only baseline. Totaling a 50% increase to our current estimate. We add this multiplier and take the high estimate as our final number.
                  </p>
                  <div className="overflow-x-auto mb-8">
                    <table className="mx-auto divide-y divide-gray-200 dark:divide-zinc-700 border border-gray-300 dark:border-zinc-800">
                      <thead className="bg-gray-50 dark:bg-zinc-800">
                        <tr>
                          <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-600">Energy</th>
                          <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-600">Water (High)</th>
                          <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider">CO2 (High)</th></tr></thead><tbody className="bg-white dark:bg-zinc-900 divide-y divide-gray-200 dark:divide-zinc-700">
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">20.92 kWh</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">94.65 L</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300">19.7 kg CO2e</td></tr></tbody>
                    </table>
                  </div>
                  <p className="mb-6">
                    Now the total amounts were accumulated over the past six months and we can divide it by the amount of days (182 days) to see our average daily usage. We end up with:
                  </p>
                  <table className="mx-auto divide-y divide-gray-200 dark:divide-zinc-700 border border-gray-300 dark:border-zinc-800">
                    <thead className="bg-gray-50 dark:bg-zinc-800">
                      <tr>
                        <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-600">Energy</th>
                        <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-600">Water (High)</th>
                        <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider">CO2 (High)</th></tr></thead><tbody className="bg-white dark:bg-zinc-900 divide-y divide-gray-200 dark:divide-zinc-700">
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">0.115 kWh per day</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">0.52 L per day</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300">0.054 kg per day</td></tr>
                      </tbody>
                    </table>
                  <p className="mb-3 mt-15">
                    Energy: The Netherlands <a href="https://www.worlddata.info/europe/netherlands/energy-consumption.php" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">per capita energy consumption </a> totals at: 6,049.13 kWh and dividing by 365 gives us 16.6 kWh per day.
                  </p>
                  <p className="mb-3">
                    Water: The CBS says that we consume about <a href="https://longreads.cbs.nl/the-netherlands-in-numbers-2023/how-many-litres-of-water-do-we-use-per-day/" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">129 litres of water per day.</a>
                  </p>
                  <p className="mb-8">
                    CO2: <a href="https://www.theglobaleconomy.com/Netherlands/Carbon_dioxide_emissions_per_capita/" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">Per capita CO2 emissions</a> total at 6.87 Tons of CO2 and dividing by 365 gives us 18.8 kg per day.
                  </p>
                  <div className="overflow-x-auto mb-8">
                    <table className="mx-auto divide-y divide-gray-200 dark:divide-zinc-700 border border-gray-300 dark:border-zinc-800">
                      <thead className="bg-gray-50 dark:bg-zinc-800">
                        <tr>
                          <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-600">Item</th>
                           <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-600">Energy</th>
                          <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider border-r border-gray-300 dark:border-zinc-600">Water (High)</th>
                          <th scope="col" className="px-2 py-1 whitespace-nowrap text-left text-xs font-medium text-gray-900 dark:text-zinc-300 uppercase tracking-wider">CO2 (High)</th></tr></thead><tbody className="bg-white dark:bg-zinc-900 divide-y divide-gray-200 dark:divide-zinc-700">
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-zinc-100 border-r border-gray-300 dark:border-zinc-600">Total of this project</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">20.92 kWh</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">94.65 L</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300">9.58 kg</td></tr><tr>
                          <td className="px-2 py-1 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-zinc-100 border-r border-gray-300 dark:border-zinc-600">Dutch daily average:</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">16.6 kWh per day</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">129 L per day</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300">18.8 kg per day</td></tr><tr>
                          <td className="px-2 py-1 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-zinc-100 border-r border-gray-300 dark:border-zinc-600">Daily average of this project (total / 365)</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">0.115 kWh per day (0.7% of daily average)</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300 border-r border-gray-300 dark:border-zinc-600">0.52 L per day (0.4% of daily average)</td>
                          <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-zinc-300">0.054 kg per day (0.28% of daily average)</td></tr>
                      </tbody>
                    </table>
                  </div>
                  <p className="mb-3">
                    This reveals that when we take the high estimate of our usage and emissions that they are comparable to that of an average dutch person's daily emissions and usage.
                  </p>
                  <p className="mb-3">
                    The energy consumption is comparable to running a 200w gaming laptop for 100 hours (20.92 kWh / 0.2 kWh)
                  </p>
                  <p className="mb-3">
                    The water consumption is comparable to two average showers (<a href="https://longreads.cbs.nl/the-netherlands-in-numbers-2023/how-many-litres-of-water-do-we-use-per-day/" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">https://longreads.cbs.nl/the-netherlands-in-numbers-2023/how-many-litres-of-water-do-we-use-per-day/</a>)
                  </p>
                  <p className="mb-3">
                    The carbon emissions are similar to driving ~48 km in a fossil fuel car. (<a href="https://www.cbs.nl/en-gb/figures/detail/85347ENG" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">https://www.cbs.nl/en-gb/figures/detail/85347ENG</a>)
                  </p>
                  <p className="mb-3">
                    Of course this just represents my personal piece of the aggregate pie that is global AI emissions and consumption. The total emissions of the entire sector are much bigger and pose serious problems to the climate and freshwater supplies.
                  </p>
                  <p className="mb-3">
                    The optimal route seems to be ensuring data centers that power AI are powered by renewables or nuclear power. Algorithmic and hardware efficiency gains can offset the increase in demand but are not keeping up with demand. With renewables we can still profit from the benefits AI could bring us while ensuring we limit the effects on climate change as much as possible.
                  </p>
                  <p className="mb-3">
                    I think that as long as we use AI and LLMs to learn, develop, improve, automate aspects of our lives that this can be justified, simply using these systems with no regard for its emissions is not acceptable. Let's take the research tool as an example. When used, the emissions that are not created because of the time saved by using the tool could offset the emission caused by the usage of AI while building and using the system. This doesn't work for every use-case, but looking at using AI from this lens does make you more conscious about applying it in ways that give back
                  </p>
                </div>
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  );
}
