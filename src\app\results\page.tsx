'use client';

import React from 'react';
import { ClipboardDocumentListIcon, LightBulbIcon, GlobeAltIcon, AcademicCapIcon, BeakerIcon, ArrowTrendingUpIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

interface CardData {
  title: string;
  icon: React.ElementType;
  content: React.ReactNode;
}

export default function ResultsPage() {
  const resultsCardData: CardData[] = [
    {
      title: "",
      icon: ClipboardDocumentListIcon,
      content: (
        <>
          {/* Section 1: Key Distinction & Evolving Focus */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <LightBulbIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
              <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Core Insight</h2>
            </div>
            <div className="pl-8">
              <p className="mb-3">The process of creating this supportive narrative has resulted in me discovering a key distinction between what I thought I should have been achieving through my SN (using AI more effectively in my workflow) and what I actually needed to be doing (Creating personal frameworks to ensure I can keep up with all AI research) and allowed me to navigate the space of possible topics for my SN effectively. Iteratively narrowing the scope of my research question over time.</p>
            </div>
          </div>

          {/* Section 2: Choosing a Website: Rationale and Benefits */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <GlobeAltIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
              <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Platform Choice</h2>
            </div>
            <div className="pl-8">
              <p className="mb-3">
                It also resulted in me deciding to create this SN in website format. It seemed interesting to do this because by automating the critical review process, the AI evaluations provided insights about software development utilizing AI tools which could be re-applied to the process of creating the website itself, creating a constructive feedback loop. 
              </p>
              <p className="mb-3">
                Furthermore, the website format results in more freedom regarding the displaying of content, adding links, images, tooltips all help to make complex topics digestible without requiring to explain each complex topic within the content itself.
              </p>
              <p className="mb-3">
                I’ve also learned about the state of AI research and the difficulty of staying up to date with its developments because of the vast amount of material being published. Sparking the idea of automating the critical review and building this website around it.
              </p>
              <p className="mb-3">
                By realising this project I've learned valuable lessons about looking at these kinds of problems in a problem solving way instead of as a limitation. Experiencing the ways we can create our own systems and tools to solve said problems has shifted my mentality to seeing potential solutions where I first saw obstacles to navigate around.
              </p>
            </div>
          </div>

          {/* Section 4: The Research Tool: Iterative Development and Impact */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <BeakerIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
              <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Tool Impact</h2>
            </div>
            <div className="pl-8">
              <p className="mb-3">
                The research tool itself is a direct result of me looking to answer my research question. I’ve designed, built and used the system iteratively. Figuring out what worked and what didn't, simplifying where needed and adding complexity at other stages. 
                I’ve successfully discovered and assessed papers using the system and have begun building my personal collection of evaluated papers that I can reference whenever I need to. 
                The tool answers my research question in part. I've conducted a process in which I designed, built and used a system (the research tool) that has assessed emerging AI research and methodologies.
              </p>
              <p className="mb-3">
                Besides the effects of the process, a result is also what the tool produces. I now have an easy to use way to take any piece of academic literature and extract useful insights.
              </p>
              <p className="mb-3">
                The extra functionalities that I have proposed in the “introduction” and in the “reflection” page intend to address the parts not directly answered as of now which are: the discovery and integration of AI research, methodologies and approaches. Discovery being done through automated scraping of publication servers for papers in related fields of research and Integration through automated notifications via slack and/or email.
              </p>
            </div>
          </div>

          {/* Section 6: Primary Result: Personal Growth Through Process */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <ArrowPathIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
              <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Personal Growth</h2>
            </div>
            <div className="pl-8">
              <p className="mb-3">
                Conclusively the biggest results for me have not been the output generated by my system, but rather the growth I have experienced during this year while creating it.
                Because of the focus towards reflection upon the process rather than the end result, reflecting on my process has now become an integral part of my workflow as it has allowed me to more effectively decide when to zoom out and work from a high level overview, planning steps ahead and systematically evaluating the current state of a project to knowing when a task requires a brute-force trial and error style approach and knowing how to use them both in tandem. 
              </p>
            </div>
          </div>
        </>
      )
    },
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 font-merriweather">
      {/* Banner Section */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-white dark:text-white mb-4">
              Results
            </h1>
            <p className="text-xl text-white dark:text-white max-w-3xl mx-auto">
              Describing the outcomes of the LLM aided research and how it answered the research question.
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Main Content Wrapper with Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        <div className="space-y-8">
          {resultsCardData.map((card, index) => {
            const IconComponent = card.icon;
            return (
              <div key={index} className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-gray-700 border-t-0 mb-8 overflow-hidden flex flex-col">
                <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                <div className="p-6 flex-grow">
                  {card.title && (
                    <div className="mb-4">
                      {/* <IconComponent className="h-7 w-7 mr-3 text-blue-500 shrink-0" /> Main card icon if title were used */}
                      <h4 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                        {card.title}
                      </h4>
                    </div>
                  )}
                  <article className="font-merriweather text-gray-700 dark:text-gray-300">
                    {card.content}
                  </article>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
