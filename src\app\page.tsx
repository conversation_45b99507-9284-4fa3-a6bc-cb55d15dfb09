'use client';

import { Inter } from 'next/font/google'
import { BookOpenIcon, UsersIcon, PuzzlePieceIcon, ChatBubbleLeftRightIcon, LightBulbIcon, EyeIcon, MagnifyingGlassIcon, ChevronRightIcon, UserIcon, SparklesIcon } from '@heroicons/react/24/outline';
import SparklesIconWithTooltip from '@/components/SparklesIconWithTooltip';
import { useEffect, useRef, useState } from 'react';

// Helper function to interpolate colors (same as navbar)
const interpolateColor = (percentage: number, color1: [number, number, number], color2: [number, number, number]): string => {
  const r = Math.round(color1[0] + percentage * (color2[0] - color1[0]));
  const g = Math.round(color1[1] + percentage * (color2[1] - color1[1]));
  const b = Math.round(color1[2] + percentage * (color2[2] - color1[2]));
  return `rgb(${r}, ${g}, ${b})`;
};

const START_COLOR_RGB: [number, number, number] = [96, 165, 250]; // Tailwind blue-400
const END_COLOR_RGB: [number, number, number] = [192, 132, 252];   // Tailwind purple-400

export default function Home() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [elementColors, setElementColors] = useState<Record<string, string>>({});

  // Refs for all elements that need gradient coloring
  const elementRefs = useRef<Record<string, HTMLElement | null>>({});

  useEffect(() => {
    const calculateColors = () => {
      if (!containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const newColors: Record<string, string> = {};

      Object.entries(elementRefs.current).forEach(([key, element]) => {
        if (element) {
          const elementRect = element.getBoundingClientRect();

          // Calculate center of the element relative to the container's left edge
          const elementCenterXPx = (elementRect.left - containerRect.left) + (elementRect.width / 2);

          let percentage = elementCenterXPx / containerRect.width;
          percentage = Math.max(0, Math.min(1, percentage)); // Clamp between 0 and 1

          newColors[key] = interpolateColor(percentage, START_COLOR_RGB, END_COLOR_RGB);
        }
      });
      setElementColors(newColors);
    };

    // Initial calculation after layout is stable
    const rafId = requestAnimationFrame(calculateColors);

    window.addEventListener('resize', calculateColors);

    return () => {
      cancelAnimationFrame(rafId);
      window.removeEventListener('resize', calculateColors);
    };
  }, []);

  return (
    <div ref={containerRef} className="min-h-screen bg-gray-50 dark:bg-zinc-900 font-merriweather">
      {/* Hero Section */}
      <section className="relative isolate text-white py-8 md:py-12 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        {/* Enhanced Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>

        {/* Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12 relative z-10">
          <div className="max-w-4xl">
            <h1 className="text-3xl md:text-4xl font-bold mb-6 text-white dark:text-white">
             Supportive Narrative - Developing an AI-Powered Research Tool.
            </h1>
            <div
              ref={el => { elementRefs.current['hero-divider'] = el; }}
              className="w-full md:w-1/2 border-t-2 border-dotted mb-8"
              style={{ borderColor: elementColors['hero-divider'] || 'rgb(192, 132, 252)' }}
            ></div>
            <p className="text-lg md:text-xl mb-8 text-white dark:text-white">
              This website documents the design, creation, and application of an AI-assisted system for analyzing and evaluating research papers.
            </p>
            <div className="flex items-center space-x-2 text-lg text-white dark:text-white opacity-80 dark:opacity/80">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span>Nino van Orsouw</span>
            </div>
          </div>
        </div>
        {/* Added Bottom Gradient Bar */}
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Site Guide Card Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16">
        {/* Standalone Title for the Site Guide section */}
        <div className="mb-12 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 flex items-center justify-center">
            <BookOpenIcon
              ref={el => { elementRefs.current['site-guide-icon'] = el; }}
              className="h-8 w-8 md:h-10 md:w-10 mr-3 shrink-0"
              style={{ color: elementColors['site-guide-icon'] || 'rgb(2, 132, 199)' }}
            />
            Site Guide
          </h2>
        </div>

        <div className="grid grid-cols-1 gap-8">
          {/* Card 1: The Website */}
          <div
            ref={el => { elementRefs.current['card1-border'] = el; }}
            className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 md:p-8 border border-gray-200 dark:border-zinc-700 border-t-4"
            style={{ borderTopColor: elementColors['card1-border'] || 'rgb(14, 165, 233)' }}
          >
            <section className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
              <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <UsersIcon
                  ref={el => { elementRefs.current['card1-icon'] = el; }}
                  className="h-6 w-6 md:h-7 md:w-7 mr-3"
                  style={{ color: elementColors['card1-icon'] || 'rgb(14, 165, 233)' }}
                />
                Welcome!
              </h3>
              <p>
                Please take a moment to read the information on this page to get started with this website.<br /> <br />
                Creating my supportive narrative as a website comes with benefits and limitations. For example; it allows readers to navigate freely, and experience the content in a non-linear way. This in turn can be confusing if a general direction is not given. That is where this page comes in.<br /><br />
                Below you will find more information, please read everything carefully.
              </p>
            </section>
          </div>

          {/* Card 2: Interpreting the Content */}
          <div
            ref={el => { elementRefs.current['card2-border'] = el; }}
            className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 md:p-8 border border-gray-200 dark:border-zinc-700 border-t-4"
            style={{ borderTopColor: elementColors['card2-border'] || 'rgb(168, 85, 247)' }}
          >
            <section className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
              <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <EyeIcon
                  ref={el => { elementRefs.current['card2-icon'] = el; }}
                  className="h-6 w-6 md:h-7 md:w-7 mr-3"
                  style={{ color: elementColors['card2-icon'] || 'rgb(168, 85, 247)' }}
                />
                Interpreting the Content
              </h3>
              <p className="mb-3">
                To ensure clarity about the origins of content, the site uses visual cues:
              </p>
              <ul
                ref={el => { elementRefs.current['card2-border-left'] = el; }}
                className="list-none space-y-3 pl-4 border-l-2"
                style={{ borderLeftColor: `${elementColors['card2-border-left'] || 'rgb(168, 85, 247)'}30` }}
              >
                <li>
                  <strong className="font-merriweather text-purple-700 dark:text-purple-300">Merriweather Font:</strong> This indicates the text is human written.
                </li>
                <li>
                  <UserIcon
                    ref={el => { elementRefs.current['card2-user-icon'] = el; }}
                    className="inline-block h-5 w-5 mr-1.5 flex-shrink-0"
                    style={{ color: elementColors['card2-user-icon'] || 'rgb(234, 179, 8)' }}
                  />
                  <strong className="text-gray-700 dark:text-gray-200 mr-1">Person Icon</strong>
                  <span className="text-gray-600 dark:text-gray-200">Indicates content is human-written.</span>
                </li>
                <li>
                  <strong className="font-roboto text-teal-600 dark:text-teal-300">Roboto Font:</strong>  <span className="font-roboto"> This indicates the text is AI generated.</span>
                </li>
                <li>
                  <SparklesIcon
                    ref={el => { elementRefs.current['card2-sparkles-icon'] = el; }}
                    className="inline-block h-5 w-5 mx-1"
                    style={{ color: elementColors['card2-sparkles-icon'] || 'rgb(245, 158, 11)' }}
                  />
                  <strong className="text-gray-700 dark:text-gray-200 mr-1">Sparkles Icon</strong>
                  <span className="text-gray-600 dark:text-gray-200">Indicates content is AI generated.</span>
                </li>
              </ul>
              <p className="mt-3">At the top right of the page, there is also an information icon. Hovering over this will display the above information so that it is accesible on any page of the website. <br /><br />
               Clarifying further, it means for instance that by looking at the navigation bar at the top of the page that the person icon tells us the content on this page is human-written.</p>
            </section>
          </div>

          {/* Card 3: Navigating This Site */}
          <div
            ref={el => { elementRefs.current['card3-border'] = el; }}
            className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 md:p-8 border border-gray-200 dark:border-zinc-700 border-t-4"
            style={{ borderTopColor: elementColors['card3-border'] || 'rgb(234, 179, 8)' }}
          >
            <section className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
              <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <MagnifyingGlassIcon
                  ref={el => { elementRefs.current['card3-icon'] = el; }}
                  className="h-6 w-6 md:h-7 md:w-7 mr-3"
                  style={{ color: elementColors['card3-icon'] || 'rgb(234, 179, 8)' }}
                />
                Navigation
              </h3>
              <p>
                Use the navigation bar at the top of the page to jump between pages. On pages like <span className="font-semibold">Methods</span> and <span className="font-semibold">Results</span>, you&apos;ll find search bars and filter options to help you pinpoint specific information within the AI-generated data.
              </p>
            </section>
          </div>

          {/* Card 4: Site Contents */}
          <div
            ref={el => { elementRefs.current['card4-border'] = el; }}
            className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 md:p-8 border border-gray-200 dark:border-zinc-700 border-t-4"
            style={{ borderTopColor: elementColors['card4-border'] || 'rgb(34, 197, 94)' }}
          >
            <section className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
              <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <PuzzlePieceIcon
                  ref={el => { elementRefs.current['card4-icon'] = el; }}
                  className="h-6 w-6 md:h-7 md:w-7 mr-3"
                  style={{ color: elementColors['card4-icon'] || 'rgb(34, 197, 94)' }}
                />
                Site Contents
              </h3>
              <p className="mb-3">
                An overview describing the pages of this website and their contents following the order of the navigation bar:
              </p>
              <div
                ref={el => { elementRefs.current['card4-border-left'] = el; }}
                className="pl-4 border-l-2"
                style={{ borderLeftColor: `${elementColors['card4-border-left'] || 'rgb(34, 197, 94)'}30` }}
              >
                <ol className="space-y-3 list-decimal pl-4">
                  <li>
                    <strong>Home:</strong> Your starting point and introduction to this supportive narrative. <span className="text-sm italic text-gray-500 dark:text-gray-400">(Human-Authored)</span>
                  </li>
                  <li>
                    <strong>Introduction:</strong> Information about the project, author and process. <span className="text-sm italic text-gray-500 dark:text-gray-400">(Human-Authored)</span>
                  </li>
                  <li>
                    <strong>Critical Review:</strong> Analysis of AI research papers, featuring:
                    <ul className="list-disc list-inside space-y-1.5 pl-4 mt-1.5 text-gray-700 dark:text-gray-300 text-sm">
                      <li>
                        Process Description: The methodology and process used to analyze the research papers. <span className="italic text-gray-500 dark:text-gray-400">(Human-Authored)</span>
                      </li>
                      <li>
                        Source Material: A browsable database of the original papers and their LLM generated evaluations. <span className="italic text-gray-500 dark:text-gray-400">(Partly AI-Generated)</span>
                        <SparklesIconWithTooltip
                          ref={el => { elementRefs.current['card4-sparkles1'] = el; }}
                          className="inline-block h-4 w-4 ml-1 align-middle"
                          style={{ color: elementColors['card4-sparkles1'] || 'rgb(245, 158, 11)' }}
                        />
                      </li>
                    </ul>
                  </li>
                  <li>
                    <strong>Methods:</strong> Browse methodologies extracted from analyzed papers by AI. <span className="text-sm italic text-gray-500 dark:text-gray-400">(AI-Generated)</span>
                    <SparklesIconWithTooltip
                      ref={el => { elementRefs.current['card4-sparkles2'] = el; }}
                      className="inline-block h-4 w-4 ml-1 align-middle"
                      style={{ color: elementColors['card4-sparkles2'] || 'rgb(245, 158, 11)' }}
                    />
                  </li>
                  <li>
                    <strong>Results:</strong> Explore AI-analyzed insights based on paper evaluation. <span className="text-sm italic text-gray-500 dark:text-gray-400">(AI-Generated)</span>
                    <SparklesIconWithTooltip
                      ref={el => { elementRefs.current['card4-sparkles3'] = el; }}
                      className="inline-block h-4 w-4 ml-1 align-middle"
                      style={{ color: elementColors['card4-sparkles3'] || 'rgb(245, 158, 11)' }}
                    />
                  </li>
                  <li>
                    <strong>Reflection:</strong> Personal insights on the project, its development and implications. <span className="text-sm italic text-gray-500 dark:text-gray-400">(Human-Authored, Under Development)</span>
                  </li>
                  <li>
                    <strong>Ethics:</strong> Discussing ethical aspects of using AI in research. and using AI to build this project. <span className="text-sm italic text-gray-500 dark:text-gray-400">(Human-Authored, Under Development)</span>
                  </li>
                  <li>
                    <strong>Glossary:</strong> Key terms defined with AI assistance and used to display site-wide tooltips. <span className="text-sm italic text-gray-500 dark:text-gray-400">(AI-Generated)</span>
                    <SparklesIconWithTooltip
                      ref={el => { elementRefs.current['card4-sparkles4'] = el; }}
                      className="inline-block h-4 w-4 ml-1 align-middle"
                      style={{ color: elementColors['card4-sparkles4'] || 'rgb(245, 158, 11)' }}
                    />
                  </li>
                </ol>
              </div>
            </section>
          </div>

          {/* Card 5: Site Capabilities & Features */}
          <div
            ref={el => { elementRefs.current['card5-border'] = el; }}
            className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 md:p-8 border border-gray-200 dark:border-zinc-700 border-t-4"
            style={{ borderTopColor: elementColors['card5-border'] || 'rgb(239, 68, 68)' }}
          >
            <section className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
              <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <LightBulbIcon
                  ref={el => { elementRefs.current['card5-icon'] = el; }}
                  className="h-6 w-6 md:h-7 md:w-7 mr-3"
                  style={{ color: elementColors['card5-icon'] || 'rgb(239, 68, 68)' }}
                />
                Features
              </h3>
              <div
                ref={el => { elementRefs.current['card5-border-left'] = el; }}
                className="pl-5 border-l-2"
                style={{ borderLeftColor: `${elementColors['card5-border-left'] || 'rgb(239, 68, 68)'}30` }}
              >
                <ul className="list-disc space-y-3 pl-4">
                  <li><strong>Interactive Exploration:</strong> Search, filter, and sort through AI-analyzed research papers in the Methods and Results sections.</li>
                  <li><strong>Mini-menu:</strong> When viewing content belonging to any specific paper, a mini-menu appears on the right to navigate between the different content of that paper.</li>
                  <li><strong>Dynamic Glossary:</strong> Key terms are explained, with inline tooltips for quick reference.</li>
                  <li><strong>Theme Switching:</strong> Use the gear icon on the top right to toggle between light/dark theme.</li>
                  <li><strong>Model Selection:</strong> Use the gear icon on the top right to switch between LLMs, this will change the content visible in the evaluation, methods and results pages.</li>
                  <li><strong>Content Transparency:</strong> Clear visual distinction between human and AI generated content.</li>
                  <li><strong>Responsive Design:</strong> Makes it possible to assess this supportive narrative from your mobile device.</li>
                </ul>
              </div>
            </section>
          </div>

          {/* Card 6: Getting Started (User Guidance) */}
          <div
            ref={el => { elementRefs.current['card6-border'] = el; }}
            className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 md:p-8 border border-gray-200 dark:border-zinc-700 border-t-4"
            style={{ borderTopColor: elementColors['card6-border'] || 'rgb(99, 102, 241)' }}
          >
            <section className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
              <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <ChatBubbleLeftRightIcon
                  ref={el => { elementRefs.current['card6-icon'] = el; }}
                  className="h-6 w-6 md:h-7 md:w-7 mr-3"
                  style={{ color: elementColors['card6-icon'] || 'rgb(99, 102, 241)' }}
                />
                Where to Start?
              </h3>
              <p className="mb-3">
                Here are a couple of ways to begin exploring this supportive narrative:
              </p>
              <ul
                ref={el => { elementRefs.current['card6-border-left'] = el; }}
                className="list-none space-y-4 pl-4 border-l-2"
                style={{ borderLeftColor: `${elementColors['card6-border-left'] || 'rgb(99, 102, 241)'}30` }}
              >
                <li>
                  <strong>Understand the Project:</strong>
                  <span className="block text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Visit the <a href="/introduction" className="text-indigo-600 dark:text-indigo-400 hover:underline font-semibold">Introduction</a> page to learn about the project's objectives, the research process, and the author.
                  </span>
                </li>
                <li>
                  <strong>Explore the Research Database:</strong>
                  <span className="block text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Go directly to the <a href="/critical-review/source-material" className="text-indigo-600 dark:text-indigo-400 hover:underline font-semibold">Critical Review's Source Material</a> to browse the database of research papers and their AI-generated evaluations.
                  </span>
                </li>
              </ul>
              <p className="mt-5">
                Feel free to explore other sections as well!
              </p>
            </section>
          </div>
        </div>
      </section>
    </div>
  )
}
