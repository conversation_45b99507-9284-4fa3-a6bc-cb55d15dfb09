'use client';

import React from 'react';
import {
  LightBulbIcon,
  WrenchScrewdriverIcon,
  ChatBubbleLeftEllipsisIcon,
  ArrowTrendingUpIcon
} from '@heroicons/react/24/outline';

interface CardData {
  title: string;
  icon: React.ElementType;
  content: React.ReactNode;
}

export default function ReflectionPage() {
  const reflectionCardData: CardData[] = [
    {
      title: "", // Consolidated card, title intentionally blank
      icon: LightBulbIcon, // Placeholder, won't be displayed with blank title
      content: (
        <>
          {/* Section 1: Research Process Evolution */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <LightBulbIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
              <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Research Process Evolution</h2>
            </div>
            <div className="pl-8">
              <p className="mb-3">The core research question, "How do I design, build, and use system(s) that discover, assess, and integrate emerging AI research, methodologies, and approaches into my audio software development workflow?" was as much a result of my process of developing this tool as the tool was an attempt to answer the research question; they both aligned over time.</p>
              <p className="mb-3">I am satisfied with the evolution of my research question. Looking back, I can see that I progressively narrowed down the scope of the question until I arrived at the current one. At the time, I did not pose the original question broadly on purpose. This allowed me to refine the question withoutchanging the topic so drastically I had to redo my entire SN. This is definitely a lesson I've learned and I will definitely apply this to future research.</p>
              <p className="mb-3">I’m glad to have made an effort last year to get the maker-researcher double profile, it provided me with a different lens to view my own work and process. Needing a short period of adjustment at the start of the year while making the switch from the “maker” mentality from the previous years (thinking about work mostly in terms of the end result and delivering satisfactory artefacts) to thinking deeply about how i was even arriving to those results in the first place, i’ve previously had trouble formalizing these processes for myself. </p>
              <p className="mb-3">Because of the double-profile, this school year and the process of making my SN I've become more balanced in the way that I approach projects. I notice that in the projects outside of the study that I have been more critical and analytical of my work processes and spend more time validating their effectiveness before following through with them. </p>
              <p className="mb-3">For example, previously when I wanted to build, let's say a neural network that performed some function. I would just take that rough idea and start building it. Running into issues along the way and then having to take a step or multiple back before being able to continue. While there isn't something inherently wrong with this approach, for projects that involve complex tasks such as programming the amount of these unforeseen issues can be so vast that the project would take much longer if conducted purely using this approach.</p>
              <p className="mb-3">On the other end we could spend a long time meticulously planning every single component and aspect of the project to such a degree that it wouldn't allow for much freedom during the process, limiting creativity but ensuring we run into less of these unforeseen problems. </p>
              <p className="mb-3">As usual, the optimal strategy lies somewhere in between and by analyzing my process over the past year I have been able to pinpoint how much of these two approaches I am usually applying when working on personal work related projects.  At the start of the year mostly working using this “brute-force” approach, primarily fueled by high motivation and a desire to get something functional off the ground quickly. To really take the time to plan out projects, drawing out ideas on paper, creating flowcharts and planning out projects thoroughly beforehand. </p>
              <p className="mb-3">This second approach also has its downsides, as in some scenarios it can definitely be very effective but it inherently introduces rigidity and therefore leaves less room for improvisation, on the spot decisions and creativity. </p>
              <p className="mb-3">The maker-research lens has enabled me to slowly but steadily approach the right balance between these two approaches.  </p>
              <p className="mb-3">I am experiencing that for every project done, By looking at how I am working, I can better guide myself when to give myself room for exploration and when to take a step back to analyze and plan out steps in advance. This has proven itself over this past year by me feeling more confident in my abilities, having less setbacks and from positive feedback by peers and colleagues. </p>
            </div>
          </div>
          
          {/* Section 2: Tool Development Journey */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <WrenchScrewdriverIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
              <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Tool Development Journey</h2>
            </div>
            <div className="pl-8">
              <p className="mb-3">I am very satisfied with what the tool has become, it's one of those projects which seem to have no end in sight, every door closed opens another. For every feature implemented two new ones are added to the to-do list. Unfortunately I have not been able to add all features that I would have liked it to have, but it does serve the purpose of showcasing the development process and as a proof of concept for what it could eventually become.</p>
              <p className="mb-3">Every LLM system used for this project has its own personality, this is decided by multiple factors: the data it was trained on, the reinforcement-learning environment, the reward function that was used during training and the system prompt that the companies append to the context window to guide the LLM in its interactions. </p>
              <p className="mb-3">This means that there are already multiple sources of potential subjectivity and bias that get layered over and color the output which we cannot access directly (except if this information was leaked somehow, as some system prompts are. <a href="https://github.com/jujumilk3/leaked-system-prompts" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">https://github.com/jujumilk3/leaked-system-prompts</a>) Let alone modify.</p>
              <p className="mb-3">In my eyes, a “good” research assistant would evaluate as objectively as possible, and while we append our own version of a “system prompt” which explicitly instructs the model to do so, we have no guarantee that our instructions will have more influence and effectively override some underlying personality-trait, goal or preference which deviate from our expected behaviour. </p>
              <p className="mb-3">I believe there are some approaches that could minimize this:</p>
              <ol className="list-decimal list-inside mb-6 ml-5 space-y-2">
                <li>
                  We have multiple LLMs evaluate the same paper and fill in the same template, then average all the scores and summarize each section (this step would require another LLM to do the summarizing, which would introduce another layer of potential bias, however small.)
                </li>
                <li>
                  Taking inspiration from LLMArena (<a href="https://lmarena.ai/" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">https://lmarena.ai/</a>) We apply a similar process to the evaluation template, context documents and prompt. We have LLMs generate a number of different criteria, instructions, stylistic rules, templates and prompt structures to use when evaluating papers. All with the same high level instruction: “Create these documents to produce the highest quality evaluation output as possible within some specified domain in research” We would evaluate papers using various combinations of the outputs generated from this and present two completed evaluations to the user each time, having them vote on the one that produces the best output in their eyes. By process of elimination we would eventually arrive at successful metrics and instructions while also having data about approaches that are not as successful.
                </li>
              </ol>
              <p className="mb-3">This is what I meant with doors closing and opening, there is a lot of potential in this tool and I am excited to continue developing it. </p>
            </div>
          </div>

          {/* Section 3: Ethical and Critical Viewpoints */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <ChatBubbleLeftEllipsisIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
              <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Ethical and Critical Viewpoints</h2>
            </div>
            <div className="pl-8">
              <p className="mb-3">We should question the validity of any output produced by AI models. How realistic and feasible is it truly to translate abstract and domain-specific knowledge to a different field or practical work? At the same time it's not easy to find a domain-expert that also happens to be an AI researcher and therefore capable of translating such information between domains with confidence. Let alone when the research covers more than two scientific domains. 
              </p>
              <p className="mb-3">LLMs all have sycophantic tendencies and especially when we are dealing with information of such complexity, such as scientific literature from a domain that is not our expertise. It can be easy to interpret a logical and thought out sounding argument as true when it was simply fabricated by the model to satisfy its personal goal of producing easy to read text.</p>
              <p className="mb-3">That is why this tool is not a replacement for critical-thinking, but serves to speed up the first stage of the research process. It should be our responsibility to ensure we do not take the information for granted and double-check critical pieces of information as we should always do.</p>
            </div>
          </div>

          {/* Section 4: Growth and Future Outlook */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <ArrowTrendingUpIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
              <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Growth and Future Outlook</h2>
            </div>
            <div className="pl-8">
              <p className="mb-3">By creating this research tool to answer my previously proposed research question, I discovered what I was really trying to answer: I'm not looking for a specific piece of knowledge or a technique. I want to create the right environment for myself to be able to continuously learn and discover new information. Now, this tool fulfills that purpose exactly while also having been an excellent journey for learning web development.</p>
              <p className="mb-3">We have to keep in mind that we are responsible for these outputs and that we cannot blame the variance and imperfections of these models. We are the ones at the end of the chain that take the information and apply it elsewhere, and it should be our responsibility to ensure we do not take the information for granted and double-check critical pieces of information.</p>
            </div>
          </div>
        </>
      )
    }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 font-merriweather">
      {/* Banner Section */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-white dark:text-white mb-4">
              Reflection
            </h1>
            <p className="text-xl text-white dark:text-white max-w-3xl mx-auto">
              Reflecting on the research process, outcomes, and personal learnings.
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Main Content Wrapper with Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        <div className="space-y-8">
          {/* Render the single consolidated card */}
          {reflectionCardData.length > 0 && (() => {
            const card = reflectionCardData[0];
            // const IconComponent = card.icon; // Icon not displayed for main card title
            return (
              <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-gray-700 border-t-0 mb-8 overflow-hidden flex flex-col">
                <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                <div className="p-6 flex-grow">
                  {card.title && (
                    <div className="mb-4">
                      {/* <IconComponent className="h-7 w-7 mr-3 text-blue-500 shrink-0" /> Main card icon if title were used */}
                      <h4 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                        {card.title}
                      </h4>
                    </div>
                  )}
                  <article className="font-merriweather text-gray-700 dark:text-gray-300">
                    {card.content}
                  </article>
                </div>
              </div>
            );
          })()}
        </div>

        
      </div>
    </div>
  );
}
