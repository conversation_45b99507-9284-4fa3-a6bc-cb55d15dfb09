'use client';

import React, { useState, useRef, useEffect, ReactNode, ElementType } from 'react';
import { UserIcon, BookOpenIcon, LightBulbIcon, ChevronDownIcon, MagnifyingGlassIcon, CogIcon, ArrowPathIcon, RocketLaunchIcon, AcademicCapIcon, FlagIcon, KeyIcon, BoltIcon } from '@heroicons/react/24/outline';

// Helper function to interpolate colors
const interpolateColor = (color1: [number, number, number], color2: [number, number, number], factor: number): string => {
  const r = Math.round(color1[0] + factor * (color2[0] - color1[0]));
  const g = Math.round(color1[1] + factor * (color2[1] - color1[1]));
  const b = Math.round(color1[2] + factor * (color2[2] - color1[2]));
  return `rgb(${r}, ${g}, ${b})`;
};

const calculateColorForElement = (
  elementRef: React.RefObject<HTMLElement | SVGSVGElement | null>,
  setColorState: (color: string) => void,
  startColor: [number, number, number],
  endColor: [number, number, number]
) => {
  if (elementRef.current) {
    const element = elementRef.current;
    const rect = element.getBoundingClientRect();
    const viewportWidth = document.documentElement.clientWidth;
    const elementCenterX = rect.left + rect.width / 2;

    const gradientStartPercent = 0.15; // Start gradient at 15% of viewport width
    const gradientEndPercent = 0.85;   // End gradient at 85% of viewport width

    const gradientStartPoint = gradientStartPercent * viewportWidth;
    const gradientEndPoint = gradientEndPercent * viewportWidth;
    let factor;

    if (elementCenterX <= gradientStartPoint) {
      factor = 0;
    } else if (elementCenterX >= gradientEndPoint) {
      factor = 1;
    } else {
      factor = (elementCenterX - gradientStartPoint) / (gradientEndPoint - gradientStartPoint);
    }
    // factor = Math.max(0, Math.min(1, factor)); // Clamping is implicitly handled by the conditions
    setColorState(interpolateColor(startColor, endColor, factor));
  }
};

// Define gradient start/end colors for elements
const ELEMENT_START_COLOR_RGB: [number, number, number] = [59, 130, 246]; // blue-500
const ELEMENT_END_COLOR_RGB: [number, number, number] = [139, 92, 246]; // purple-500
// For borders that might want a slightly different end color (e.g., darker purple)
const BORDER_END_COLOR_DARKER_RGB: [number, number, number] = [126, 34, 206]; // purple-600

const initialElementColor: string = 'transparent';

interface CardDataItem {
  id: number;
  title: string;
  icon: ElementType;
  iconColorClass: string;
  content: ReactNode;
}

export default function Introduction() {
  const [openCardIndex, setOpenCardIndex] = useState<number | null>(null);

  // State for dynamic colors - Research Focus Area Cards
  const [devCardBorderColor, setDevCardBorderColor] = useState<string>(initialElementColor);
  const [devCardIconColor, setDevCardIconColor] = useState<string>(initialElementColor);
  const [promptCardBorderColor, setPromptCardBorderColor] = useState<string>(initialElementColor);
  const [promptCardIconColor, setPromptCardIconColor] = useState<string>(initialElementColor);
  const [agenticCardBorderColor, setAgenticCardBorderColor] = useState<string>(initialElementColor);
  const [agenticCardIconColor, setAgenticCardIconColor] = useState<string>(initialElementColor);

  // Refs for Research Focus Area Card elements
  const devCardRef = useRef<HTMLDivElement>(null); // For border
  const devIconRef = useRef<SVGSVGElement>(null);
  const promptCardRef = useRef<HTMLDivElement>(null); // For border
  const promptIconRef = useRef<SVGSVGElement>(null);
  const agenticCardRef = useRef<HTMLDivElement>(null); // For border
  const agenticIconRef = useRef<SVGSVGElement>(null);

  // State for dynamic colors - Collapsible Cards (will be an array of objects)
  // Each object: { headerIconColor: string, chevronColor: string, topBorderColor: string, contentIconColors: { [key: string]: string } }
  const [collapsibleCardColors, setCollapsibleCardColors] = useState<Array<any>>([]);

  // Refs for Collapsible Card elements
  const collapsibleCardButtonRefs = useRef<Array<React.RefObject<HTMLButtonElement>>>([]);
  const collapsibleHeaderIconRefs = useRef<Array<React.RefObject<SVGSVGElement>>>([]);
  const collapsibleChevronRefs = useRef<Array<React.RefObject<SVGSVGElement>>>([]);
  const expandedCardContentRef = useRef<HTMLDivElement>(null); // For the currently open card's content area
  // Refs for icons within card content will need a more complex structure, e.g., array of objects of refs
  const collapsibleContentIconRefs = useRef<Array<Record<string, React.RefObject<SVGSVGElement>>>>([]);

  const cardData: CardDataItem[] = React.useMemo(() => [
    {
      id: 0,
      title: "The Research Objective",
      icon: LightBulbIcon,
      iconColorClass: "border-t-teal-500 dark:border-t-teal-400",
      content: (
        <>
          {/* Section 1: Evolution of the Objective */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <LightBulbIcon className="h-6 w-6 mr-3 text-teal-500 dark:text-teal-400 shrink-0" />
              Finding the Right Question
            </h4>
            <div className="pl-9"> 
              <p className="text-gray-700 dark:text-gray-300 mb-2">
                The path to the current research objective involved an iterative process. Initially broad, the first topic was: "The Role of AI in expanding the creative process." This was later refined to "Effectively applying artificial intelligence as a music technologist."
              </p>
              <p className="text-gray-700 dark:text-gray-300">
                With this second iteration of the research question I began working on my supportive narrative. But even then, as described in my SN outline document (draft version of the concept for my supportive narrative that was handed in during the midterm of this year), I still considered this to be a working title. While working on this first version the definitive research question revealed itself to me.
              </p>
            </div>
          </div>

          {/* Section 2: The Core Research Question */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <MagnifyingGlassIcon className="h-6 w-6 mr-3 text-teal-500 dark:text-teal-400 shrink-0" />
              The Core Research Question
            </h4>
            <div className="pl-9 border-l-2 border-teal-500/30 dark:border-teal-400/30">
              <p className="py-2 px-3 font-medium text-lg text-teal-700 dark:text-teal-300">
                <em>How do I design, build and use system(s) that discover, assess, and integrate emerging AI research, methodologies and approaches into my audio software development workflow?</em>
              </p>
            </div>
          </div>

          {/* Section 3: The Realization */}
          <div>
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <KeyIcon className="h-6 w-6 mr-3 text-teal-500 dark:text-teal-400 shrink-0" />
              Key Realization
            </h4>
            <div className="pl-9">
              <p className="text-gray-700 dark:text-gray-300 mb-2">
                The more time I spent considering the possibility if certain techniques or approaches could improve my workflow, to conceptualize faster, design better software or debug more effectively. The more I realized that due to the ever-increasing speed of AI development and its expanding capabilities, these specific methods could quickly become irrelevant or even obsolete.
              </p>
              <p className="text-gray-700 dark:text-gray-300">
                This realization led to the current research question, which is centered around building systems and tools that can continuously adapt and integrate the latest AI advancements, rather than focusing on specific techniques that may quickly become outdated.
              </p>
            </div>
          </div>
        </>
      ),
    },
    {
      id: 1,
      title: "The Research Tool",
      icon: BookOpenIcon,
      iconColorClass: "border-t-purple-500 dark:border-t-purple-400",
      content: (
        <>
          {/* Section 1: How It Works */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <CogIcon className="h-6 w-6 mr-3 text-purple-500 dark:text-purple-400 shrink-0" />
              How The System Operates
            </h4>
            <p className="text-gray-700 dark:text-gray-300 mb-3 pl-9">
              The result is this website and the automated research tool, which are tightly coupled. The system operates as follows:
            </p>
            <ol className="list-decimal list-outside space-y-2 pl-20 text-gray-700 dark:text-gray-300 border-l-2 border-purple-500/30 dark:border-purple-400/30 py-2">
              <li className="mb-1 pl-2">
                A research paper (in PDF format) is selected as input.
              </li>
              <li className="mb-1 pl-2">
                The PDF is converted to a markdown format using a neural network (I used the marker-pdf tool), making it interpretable for the LLM.
              </li>
              <li className="mb-1 pl-2">
                This markdown content, along with detailed instructions and an evaluation template, is fed to the LLM in a single prompt.
              </li>
              <li className="mb-1 pl-2">
                The LLM analyzes the paper's content and fills in the evaluation template according to the provided context and instructions.
              </li>
              <li className="pl-2">
                The completed evaluation, in JSON format, is then saved to the <code>public/papers/evaluations</code> directory.
              </li>
              <li className="pl-2">
                Now the contents from the filled in JSON evaluation template are used to dynamically populate the website.
              </li>
            </ol>
            <p className="text-gray-700 dark:text-gray-300 mb-3 pl-9">
             <br /><i>NOTE: The context, template and prompt used for this process can be viewed by navigating to the <code>method</code> page. </i>
            </p>
          </div>

          {/* Section 2: Dynamic Content & Usage */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <ArrowPathIcon className="h-6 w-6 mr-3 text-purple-500 dark:text-purple-400 shrink-0" />
              Dynamic Content & Practical Usage
            </h4>
            <div className="pl-7">
              <p className="text-gray-700 dark:text-gray-300 mb-3">
                The website is designed to dynamically load these JSON evaluation files. It then displays the contents of these evaluations in a user-friendly way. Due to this dynamic loading, simply adding a new JSON evaluation file to the evaluations directory automatically updates the website, adding the new paper to the list and displaying its contents and evaluation.
              </p>
              <p className="text-gray-700 dark:text-gray-300">
                After analyzing papers, I can manually scroll through the database and pick the most relevant based on the LLM assigned score and inspect its contents. I can go to the methods page where I can learn about how a paper's methods can be implemented in a step-by-step manner or in an audio software related task. Finally, there is the results page which describes contextualized insights, taking the paper's core ideas and applying them to relevant aspects of an audio software developers typical work.
              </p>
            </div>
          </div>

          {/* Section 3: Future Vision */}
          <div>
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <RocketLaunchIcon className="h-6 w-6 mr-3 text-purple-500 dark:text-purple-400 shrink-0" />
              Future Vision
            </h4>
            <p className="text-gray-700 dark:text-gray-300 mb-3 pl-9">
              While the tool is currently functional, I have more features that I wish to implement. The long-term vision for this project extends to a more autonomous AI-powered research assistant. Future iterations could include features like:
            </p>
            <ul className="list-disc list-outside space-y-2 pl-20 text-gray-700 dark:text-gray-300 border-l-2 border-purple-500/30 dark:border-purple-400/30 py-2">
              <li className="mb-1 pl-2">Direct PDF uploads (with built in neural conversion to markdown) or uploading through a URL.</li>
              <li className="mb-1 pl-2">Automated scanning of pre-print servers (e.g., arXiv) and peer-reviewed journals and downloading relevant papers.</li>
              <li className="mb-1 pl-2">Automated evaluation of suitable papers, and added to the site if their evaluated score passes a threshold value.</li>
              <li className="mb-1 pl-2">Automated notifications (email, Slack) if a paper is autonomously evaluated, exceeds the threshold score and is added to the website.</li>
              <li className="pl-2">Allowing configuration of the prompt, evaluation criteria and context to allow for personalized use.</li>
            </ul>
          </div>
        </>
      ),
    },
    {
      id: 2,
      title: "The Author",
      icon: UserIcon,
      iconColorClass: "border-t-blue-500 dark:border-t-blue-400",
      content: (
        <>
          {/* Section 1: Initial Spark */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <BoltIcon className="h-6 w-6 mr-3 text-blue-500 dark:text-blue-400 shrink-0" />
              Acceleration
            </h4>
            <div className="pl-9">
              <p className="text-gray-700 dark:text-gray-300">
                After catching wind of the fast advancements in AI back in the summer of 2023, I was mind-blown and awe-inspired at the same time. The potential impact of this technology is enourmous and I was convinced that this technology was going to change the world, and that it would be wise to get familliar with it. <br /> <br />
                I feel fortunate to be at this position in my life during such a pivotal moment in human history. While many are skeptical, I am certain this will lead to scientific advancements like we have never seen and intend to follow its progress closely.
              </p>
            </div>
          </div>

          {/* Section 2: Bridging Interests & Studies */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <AcademicCapIcon className="h-6 w-6 mr-3 text-blue-500 dark:text-blue-400 shrink-0" />
              AI in Music Technology
            </h4>
            <div className="pl-9">
              <p className="text-gray-700 dark:text-gray-300">
                I was looking for a way to incorporate this new technology and newfound interestinto my studies and existing work and found the application of neural networks in audio plugins especially promising. With basically no prior experience but a strong drive to learn I managed to land an internship doing exactly this: learning how to build plugins and integrate neural networks into them. <br /> <br />
                This was a great success and further reinforced my feeling that I was on the right track. Ever since I have continued to work together with my former intership supervisor. And all our projects are audio and AI related, which is exactly what I wanted.
              </p>
            </div>
          </div>

          {/* Section 3: Goals and Aspirations */}
          <div>
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <FlagIcon className="h-6 w-6 mr-3 text-blue-500 dark:text-blue-400 shrink-0" />
              Goals
            </h4>
            <div className="pl-9">
              <p className="text-gray-700 dark:text-gray-300">
                I'm looking to improve my skills across the board and to stay in the now by applying AI tools into my workflow where compatible and useful, and building plugins that leverage neural networks for creative purposes. This supportive narrative serves as one of my attempts to realize these goals.
              </p>
            </div>
          </div>
        </>
      ),
    }
  ], []);

  const handleCardToggle = (index: number) => {
    setOpenCardIndex(openCardIndex === index ? null : index);
  };

  const updateCollapsibleColor = (index: number, property: string, color: string) => {
    setCollapsibleCardColors(prevColors => {
      // Ensure prevColors[index] exists and the color is actually different before updating
      if (!prevColors[index] || prevColors[index][property] === color) {
        return prevColors; // Return the old state if no change or if the target item doesn't exist
      }

      const newColors = [...prevColors];
      // newColors[index] is guaranteed to exist here due to the check above
      newColors[index] = { ...newColors[index], [property]: color };
      return newColors;
    });
  };

  const calculateColors = () => {
    // Research Focus Area Cards
    calculateColorForElement(devCardRef, setDevCardBorderColor, ELEMENT_START_COLOR_RGB, BORDER_END_COLOR_DARKER_RGB);
    calculateColorForElement(devIconRef, setDevCardIconColor, ELEMENT_START_COLOR_RGB, ELEMENT_END_COLOR_RGB);
    calculateColorForElement(promptCardRef, setPromptCardBorderColor, ELEMENT_START_COLOR_RGB, BORDER_END_COLOR_DARKER_RGB);
    calculateColorForElement(promptIconRef, setPromptCardIconColor, ELEMENT_START_COLOR_RGB, ELEMENT_END_COLOR_RGB);
    calculateColorForElement(agenticCardRef, setAgenticCardBorderColor, ELEMENT_START_COLOR_RGB, BORDER_END_COLOR_DARKER_RGB);
    calculateColorForElement(agenticIconRef, setAgenticCardIconColor, ELEMENT_START_COLOR_RGB, ELEMENT_END_COLOR_RGB);
    
    // Collapsible Cards
    if (cardData && collapsibleCardButtonRefs.current.length === cardData.length && collapsibleCardColors.length === cardData.length) {
      cardData.forEach((_, index) => {
        // Card Button Border
        calculateColorForElement(collapsibleCardButtonRefs.current[index], (color) => {
          updateCollapsibleColor(index, 'cardButtonBorderColor', color);
        }, ELEMENT_START_COLOR_RGB, BORDER_END_COLOR_DARKER_RGB);
        
        // Header Icon
        calculateColorForElement(collapsibleHeaderIconRefs.current[index], (color) => {
          updateCollapsibleColor(index, 'headerIconColor', color);
        }, ELEMENT_START_COLOR_RGB, ELEMENT_END_COLOR_RGB);

        // Chevron Icon
        calculateColorForElement(collapsibleChevronRefs.current[index], (color) => {
          updateCollapsibleColor(index, 'chevronColor', color);
        }, ELEMENT_START_COLOR_RGB, ELEMENT_END_COLOR_RGB);
      });

      // Expanded Card Content Border (only if a card is open)
      if (openCardIndex !== null && expandedCardContentRef.current) {
        calculateColorForElement(expandedCardContentRef, (color) => {
          // Ensure the specific open card's expandedContentBorderColor is updated
          // This assumes updateCollapsibleColor can handle a general property or we adapt it
          // For now, let's assume collapsibleCardColors[openCardIndex] exists due to initialization
          if (collapsibleCardColors[openCardIndex]) {
             updateCollapsibleColor(openCardIndex, 'expandedContentBorderColor', color);
          }
        }, ELEMENT_START_COLOR_RGB, BORDER_END_COLOR_DARKER_RGB);
      }
    }
    // TODO: Add logic for collapsibleContentIconRefs if they become used
  };

  useEffect(() => {
    if (cardData && collapsibleCardButtonRefs.current.length === cardData.length && collapsibleCardColors.length === cardData.length) {
        calculateColors(); // Initial calculation
    }
    window.addEventListener('scroll', calculateColors, true); // Use capture phase for scroll
    window.addEventListener('resize', calculateColors);
    
    return () => {
      window.removeEventListener('scroll', calculateColors, true);
      window.removeEventListener('resize', calculateColors);
    };
  }, [collapsibleCardColors, openCardIndex, cardData]); // Add openCardIndex and cardData to dependencies

  // The cardData definition has been moved before the useEffect hook.

  useEffect(() => {
    // Initialize collapsibleCardColors state and refs for collapsible cards
    if (cardData && cardData.length > 0) {
      setCollapsibleCardColors(
        cardData.map(() => ({
          headerIconColor: initialElementColor,
          chevronColor: initialElementColor,
          cardButtonBorderColor: initialElementColor,
          expandedContentBorderColor: initialElementColor, // This will be for the currently open one
        }))
      );

      collapsibleCardButtonRefs.current = cardData.map(
        (_, i) => collapsibleCardButtonRefs.current[i] ?? React.createRef<HTMLButtonElement>()
      );
      collapsibleHeaderIconRefs.current = cardData.map(
        (_, i) => collapsibleHeaderIconRefs.current[i] ?? React.createRef<SVGSVGElement>()
      );
      collapsibleChevronRefs.current = cardData.map(
        (_, i) => collapsibleChevronRefs.current[i] ?? React.createRef<SVGSVGElement>()
      );
    }
  }, [cardData]); // Rerun if cardData instance changes, or cardData.length for shallow check

  return (
    <div className="min-h-screen bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 font-merriweather">
      {/* Banner Section */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-white dark:text-white mb-4">
              Introduction
            </h1>
            <p className="text-xl text-white dark:text-white max-w-3xl mx-auto">
              Information about this supportive narrative, the tool and the author.
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Main Content Wrapper */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">


        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          {cardData.map((card, index) => {
            // iconTextColorClass logic is removed as icon color is now dynamic
            return (
              <button
                key={card.id}
                ref={collapsibleCardButtonRefs.current[index]}
                onClick={() => handleCardToggle(index)}
                className={`w-full flex items-center justify-between p-4 md:p-6 text-left focus:outline-none rounded-lg shadow-sm dark:shadow-zinc-800/30 border-t-4 ${openCardIndex === index ? 'bg-gray-100 dark:bg-zinc-800 scale-105 shadow-md' : 'bg-white dark:bg-zinc-900 hover:bg-gray-50 dark:hover:bg-zinc-800/60'} transition-all duration-150 ease-in-out`}
                style={{
                  borderTopColor: collapsibleCardColors[index]?.cardButtonBorderColor,
                  transition: 'border-color 0.3s ease-in-out, transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out'
                }}
              >
                <div className="flex items-center">
                  {card.icon && 
                    <card.icon 
                      ref={collapsibleHeaderIconRefs.current[index]} 
                      className={`h-6 w-6 md:h-7 md:w-7 mr-3 shrink-0`} 
                      style={{ 
                        color: collapsibleCardColors[index]?.headerIconColor,
                        transition: 'color 0.3s ease-in-out'
                      }}
                    />}
                  <h2 className="text-lg md:text-xl font-semibold text-gray-800 dark:text-gray-100">{card.title}</h2>
                </div>
                <ChevronDownIcon
                  ref={collapsibleChevronRefs.current[index]}
                  className={`h-5 w-5 md:h-6 md:w-6 transform transition-transform duration-200 shrink-0 ${openCardIndex === index ? 'rotate-180' : ''}`}
                  style={{
                    color: collapsibleCardColors[index]?.chevronColor,
                    transition: 'color 0.3s ease-in-out, transform 0.2s ease-in-out'
                  }}
                />
              </button>
            );
          })}
        </div>

        {openCardIndex !== null && collapsibleCardColors[openCardIndex] && (
          <div 
            key={openCardIndex} // Added key here
            ref={expandedCardContentRef}
            className={`bg-white dark:bg-zinc-900 rounded-lg shadow-lg dark:shadow-gray-800/30 border border-gray-200 dark:border-gray-700 p-6 md:p-8 border-t-4 mt-0 mb-12 animate-fadeIn`}
            style={{
              borderTopColor: collapsibleCardColors[openCardIndex]?.expandedContentBorderColor,
              transition: 'border-color 0.3s ease-in-out'
            }}
          >
            <style jsx global>{`
              @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
              }
              .animate-fadeIn {
                animation: fadeIn 0.3s ease-out forwards;
              }
            `}</style>
            <div className="prose prose-sm sm:prose-base dark:prose-invert max-w-none text-gray-700 dark:text-gray-300">
              {cardData[openCardIndex].content}
            </div>
          </div>
        )}

        <div className="mb-8 rounded-lg shadow-lg dark:shadow-gray-800/30 bg-gradient-to-r from-teal-500 via-purple-500 to-blue-500 pt-1">
          <div className="bg-white dark:bg-zinc-900 rounded-b-lg p-6 md:p-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">Research Focus Areas</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* AI-Augmented Development Card */}
              <div ref={devCardRef} className="flex flex-col items-center p-6 bg-white dark:bg-zinc-900 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 border-t-4" style={{ borderTopColor: devCardBorderColor, transition: 'border-color 0.3s ease-in-out' }}>
                <svg ref={devIconRef} xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-4 shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5} style={{ color: devCardIconColor, transition: 'color 0.3s ease-in-out' }}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M17.25 6.75L22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3l-4.5 16.5" />
                </svg>
                <h3 className="text-xl font-semibold text-center text-gray-900 dark:text-gray-100 mb-2">AI-Augmented Development</h3>
                <p className="text-base text-center text-gray-700 dark:text-gray-300">Exploring how AI can enhance writing, debugging, and testing code</p>
              </div>

              {/* Prompt Engineering Card */}
              <div ref={promptCardRef} className="flex flex-col items-center p-6 bg-white dark:bg-zinc-900 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 border-t-4" style={{ borderTopColor: promptCardBorderColor, transition: 'border-color 0.3s ease-in-out' }}>
                <svg ref={promptIconRef} xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-4 shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5} style={{ color: promptCardIconColor, transition: 'color 0.3s ease-in-out' }} >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.5 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 16.12l-1.18-3.243-3.243-1.18 3.243-1.18 1.18-3.243 1.18 3.243 3.243 1.18-3.243 1.18-1.18 3.243z" />
                </svg>
                <h3 className="text-xl font-semibold text-center text-gray-900 dark:text-gray-100 mb-2">Prompt Engineering</h3>
                <p className="text-base text-center text-gray-700 dark:text-gray-300">Crafting effective prompts to guide AI models for desired outcomes</p>
              </div>

              {/* Agentic Workflows Card */}
              <div ref={agenticCardRef} className="flex flex-col items-center p-6 bg-white dark:bg-zinc-900 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 border-t-4" style={{ borderTopColor: agenticCardBorderColor, transition: 'border-color 0.3s ease-in-out' }}>
                <svg ref={agenticIconRef} xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-4 shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5} style={{ color: agenticCardIconColor, transition: 'color 0.3s ease-in-out' }} >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                </svg>
                <h3 className="text-xl font-semibold text-center text-gray-900 dark:text-gray-100 mb-2">Agentic Workflows</h3>
                <p className="text-base text-center text-gray-700 dark:text-gray-300">Developing AI agents that can perform tasks and make decisions autonomously</p>
              </div>
            </div>
          </div>
        </div>

      </div> {/* End Main Content Wrapper */}
    </div> // End Page Div
  );
}
