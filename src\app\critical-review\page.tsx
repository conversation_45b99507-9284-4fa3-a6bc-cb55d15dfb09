'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { SparklesIcon, UserIcon, CogIcon, ArchiveBoxIcon } from '@heroicons/react/24/outline';

// Helper function to interpolate colors
const interpolateColor = (color1: [number, number, number], color2: [number, number, number], factor: number): string => {
  const r = Math.round(color1[0] + factor * (color2[0] - color1[0]));
  const g = Math.round(color1[1] + factor * (color2[1] - color1[1]));
  const b = Math.round(color1[2] + factor * (color2[2] - color1[2]));
  return `rgb(${r}, ${g}, ${b})`;
};

// Define gradient start/end colors for HERO icon parts
const HERO_BG_START_COLOR: [number, number, number] = [29, 78, 216]; // blue-700
const HERO_BG_END_COLOR: [number, number, number] = [126, 34, 206]; // purple-700
const HERO_BORDER_START_COLOR: [number, number, number] = [96, 165, 250]; // blue-400
const HERO_BORDER_END_COLOR: [number, number, number] = [192, 132, 252]; // purple-400
const HERO_SVG_TEXT_START_COLOR: [number, number, number] = [147, 197, 253]; // blue-300
const HERO_SVG_TEXT_END_COLOR: [number, number, number] = [216, 180, 254]; // purple-300

// Define gradient start/end colors for CARD elements
const CARD_ELEMENT_START_COLOR_RGB: [number, number, number] = [59, 130, 246]; // blue-500
const CARD_ELEMENT_END_COLOR_RGB: [number, number, number] = [139, 92, 246]; // purple-500
const CARD_BORDER_END_COLOR_DARKER_RGB: [number, number, number] = [126, 34, 206]; // purple-600

interface HeroIconColors {
  bg: string;
  border: string;
  svg: string;
}

const initialHeroIconColors: HeroIconColors = { bg: 'transparent', border: 'transparent', svg: 'transparent' };
const initialElementColor: string = 'transparent';

export default function CriticalReview() {
  // Hero Icons State
  const [analysisIconColors, setAnalysisIconColors] = useState<HeroIconColors>(initialHeroIconColors);
  const [evaluationIconColors, setEvaluationIconColors] = useState<HeroIconColors>(initialHeroIconColors);
  const [integrationIconColors, setIntegrationIconColors] = useState<HeroIconColors>(initialHeroIconColors);

  // Card Elements State - Method Card
  const [methodCardBorderColor, setMethodCardBorderColor] = useState<string>(initialElementColor);
  const [methodCogIconColor, setMethodCogIconColor] = useState<string>(initialElementColor);
  const [methodUserIconColor, setMethodUserIconColor] = useState<string>(initialElementColor);
  const [methodArrowColor, setMethodArrowColor] = useState<string>(initialElementColor);

  // Card Elements State - Source Material Card
  const [sourceMaterialCardBorderColor, setSourceMaterialCardBorderColor] = useState<string>(initialElementColor);
  const [sourceArchiveIconColor, setSourceArchiveIconColor] = useState<string>(initialElementColor);
  const [sourceSparklesIconColor, setSourceSparklesIconColor] = useState<string>(initialElementColor);
  const [sourceArrowColor, setSourceArrowColor] = useState<string>(initialElementColor);

  // Refs for Hero Icons
  const iconsContainerRef = useRef<HTMLDivElement>(null);
  const analysisIconRef = useRef<HTMLDivElement>(null);
  const evaluationIconRef = useRef<HTMLDivElement>(null);
  const integrationIconRef = useRef<HTMLDivElement>(null);

  // Refs for Method Card Elements
  const methodCardRef = useRef<HTMLDivElement>(null);
  const methodCogIconRef = useRef<SVGSVGElement>(null);
  const methodUserIconRef = useRef<SVGSVGElement>(null);
  const methodArrowRef = useRef<HTMLSpanElement>(null);

  // Refs for Source Material Card Elements
  const sourceMaterialCardRef = useRef<HTMLDivElement>(null);
  const sourceArchiveIconRef = useRef<SVGSVGElement>(null);
  const sourceSparklesIconRef = useRef<SVGSVGElement>(null);
  const sourceArrowRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    const calculateColors = () => {
      const viewportWidth = document.documentElement.clientWidth;
      if (!viewportWidth) return;

      // Calculate Hero Icon Colors
      if (iconsContainerRef.current && analysisIconRef.current && evaluationIconRef.current && integrationIconRef.current) {
        const containerRect = iconsContainerRef.current.getBoundingClientRect();
        const heroIconRefs = [analysisIconRef, evaluationIconRef, integrationIconRef];
        const setHeroColorStates = [setAnalysisIconColors, setEvaluationIconColors, setIntegrationIconColors];

        heroIconRefs.forEach((iconRef, index) => {
          if (iconRef.current) {
            const iconRect = iconRef.current.getBoundingClientRect();
            let iconCenterX = (iconRect.left - containerRect.left) + (iconRect.width / 2);
            let factor = Math.max(0, Math.min(1, iconCenterX / containerRect.width));
            
            setHeroColorStates[index]({
              bg: interpolateColor(HERO_BG_START_COLOR, HERO_BG_END_COLOR, factor),
              border: interpolateColor(HERO_BORDER_START_COLOR, HERO_BORDER_END_COLOR, factor),
              svg: interpolateColor(HERO_SVG_TEXT_START_COLOR, HERO_SVG_TEXT_END_COLOR, factor),
            });
          }
        });
      }

      // Helper to calculate color for card elements based on viewport position
      const calculateViewportColor = (ref: React.RefObject<HTMLElement | SVGSVGElement | null>, setColor: (color: string) => void, startColor: [number,number,number], endColor: [number,number,number]) => {
        if (ref.current) {
          const rect = ref.current.getBoundingClientRect();
          const elementCenterX = rect.left + rect.width / 2;
          let factor = Math.max(0, Math.min(1, elementCenterX / viewportWidth));
          setColor(interpolateColor(startColor, endColor, factor));
        }
      };

      // Method Card Elements
      calculateViewportColor(methodCardRef, setMethodCardBorderColor, CARD_ELEMENT_START_COLOR_RGB, CARD_BORDER_END_COLOR_DARKER_RGB); // For border bg
      calculateViewportColor(methodCogIconRef, setMethodCogIconColor, CARD_ELEMENT_START_COLOR_RGB, CARD_ELEMENT_END_COLOR_RGB);
      calculateViewportColor(methodUserIconRef, setMethodUserIconColor, CARD_ELEMENT_START_COLOR_RGB, CARD_ELEMENT_END_COLOR_RGB);
      calculateViewportColor(methodArrowRef, setMethodArrowColor, CARD_ELEMENT_START_COLOR_RGB, CARD_ELEMENT_END_COLOR_RGB);

      // Source Material Card Elements
      calculateViewportColor(sourceMaterialCardRef, setSourceMaterialCardBorderColor, CARD_ELEMENT_START_COLOR_RGB, CARD_BORDER_END_COLOR_DARKER_RGB); // For border bg
      calculateViewportColor(sourceArchiveIconRef, setSourceArchiveIconColor, CARD_ELEMENT_START_COLOR_RGB, CARD_ELEMENT_END_COLOR_RGB);
      calculateViewportColor(sourceSparklesIconRef, setSourceSparklesIconColor, CARD_ELEMENT_START_COLOR_RGB, CARD_ELEMENT_END_COLOR_RGB);
      calculateViewportColor(sourceArrowRef, setSourceArrowColor, CARD_ELEMENT_START_COLOR_RGB, CARD_ELEMENT_END_COLOR_RGB);
    };

    calculateColors();
    window.addEventListener('resize', calculateColors);
    return () => window.removeEventListener('resize', calculateColors);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-zinc-900 font-merriweather">
      {/* Hero Section - Updated to match Home/Literature Analysis Banner Style */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        {/* Standard Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          {/* Title Section (Existing Sizing Retained) */}
          <div className="text-center mb-10 md:mb-14 font-merriweather">
            <h1 className="text-4xl font-bold text-white dark:text-white mb-4">Critical Review</h1>
            <p className="text-xl text-white dark:text-white max-w-3xl mx-auto">
              A collection of research papers evaluated by AI.
            </p>
          </div>
        </div>

        {/* Process Steps Icons & Full-Width Line - Positioned at the bottom */}
        <div className="absolute bottom-0 left-0 right-0 w-full px-4 sm:px-6 lg:px-8 z-10">
          <div ref={iconsContainerRef} className="relative flex justify-between max-w-4xl mx-auto z-10 -mb-14">
            {/* Analysis Step */}
            <div ref={analysisIconRef} className="flex flex-col items-center z-10">
              <div 
                className="w-16 h-16 rounded-full border-4 flex items-center justify-center mb-3 transition-colors duration-300"
                style={{ backgroundColor: analysisIconColors.bg, borderColor: analysisIconColors.border }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: analysisIconColors.svg }}>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <span className="text-sm font-medium text-black dark:text-white font-sans">Analysis</span>
            </div>

            {/* Evaluation Step */}
            <div ref={evaluationIconRef} className="flex flex-col items-center z-10">
              <div 
                className="w-16 h-16 rounded-full border-4 flex items-center justify-center mb-3 transition-colors duration-300"
                style={{ backgroundColor: evaluationIconColors.bg, borderColor: evaluationIconColors.border }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: evaluationIconColors.svg }}>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <span className="text-sm font-medium text-black dark:text-white font-sans">Evaluation</span>
            </div>

            {/* Integration Step */}
            <div ref={integrationIconRef} className="flex flex-col items-center z-10">
              <div 
                className="w-16 h-16 rounded-full border-4 flex items-center justify-center mb-3 transition-colors duration-300"
                style={{ backgroundColor: integrationIconColors.bg, borderColor: integrationIconColors.border }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: integrationIconColors.svg }}>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <span className="text-sm font-medium text-black dark:text-white font-sans">Integration</span>
            </div>
          </div>
        </div>
        {/* Bottom Gradient Bar - This is the reference gradient */}
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-r from-blue-500 to-purple-600"></div>
      </section>

      {/* Main Page Content (Cards) - Add top padding to account for overlapping icons */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 pt-20 md:pt-24">
        {/* Main Sections Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* The Method Section */}
          <Link
            href="/method"
            className="group font-sans"
          >
            <div ref={methodCardRef} className="relative bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 p-8 h-full hover:shadow-md transition-shadow border border-gray-200 dark:border-gray-700">
              <div className="absolute top-0 left-0 right-0 h-1 rounded-t-lg transition-colors duration-300" style={{ backgroundColor: methodCardBorderColor }}></div>
              <div className="flex items-start justify-between">
                <div>
                  <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors font-merriweather flex items-center">
                    <CogIcon ref={methodCogIconRef} className="h-6 w-6 mr-2 flex-shrink-0 transition-colors duration-300" style={{ color: methodCogIconColor }} />
                    The Method
                    <UserIcon ref={methodUserIconRef} className="ml-2 h-5 w-5 flex-shrink-0 transition-colors duration-300" style={{ color: methodUserIconColor }} />
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-4 font-merriweather">
                    Here you will find the exact steps and prompt content used to evaluate each paper.
                  </p>
                  <ul className="text-gray-600 dark:text-gray-300 list-disc list-inside space-y-1 font-merriweather">
                    <li>Context Documents</li>
                    <li>Scoring Criteria</li>
                    <li>Methods Extraction</li>
                    <li>Results & Insights</li>
                    <li>JSON Evaluation Template</li>
                  </ul>
                </div>
                <span ref={methodArrowRef} className="group-hover:translate-x-1 transition-transform transition-colors duration-300" style={{ color: methodArrowColor }}>→</span>
              </div>
            </div>
          </Link>

          {/* Source Material Section */}
          <Link
            href="/critical-review/source-material"
            className="group font-sans"
          >
            <div ref={sourceMaterialCardRef} className="relative bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 p-8 h-full hover:shadow-md transition-shadow border border-gray-200 dark:border-gray-700">
              <div className="absolute top-0 left-0 right-0 h-1 rounded-t-lg transition-colors duration-300" style={{ backgroundColor: sourceMaterialCardBorderColor }}></div>
              <div className="flex items-start justify-between">
                <div>
                  <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors flex items-center">
                    <ArchiveBoxIcon ref={sourceArchiveIconRef} className="h-6 w-6 mr-2 flex-shrink-0 transition-colors duration-300" style={{ color: sourceArchiveIconColor }} />
                    Source Material
                    <SparklesIcon ref={sourceSparklesIconRef} className="ml-2 h-5 w-5 transition-colors duration-300" style={{ color: sourceSparklesIconColor }} />
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-4 font-roboto">
                    Access the collection of research papers and their evaluations. <br /><br />
                  </p>
                  <ul className="text-gray-600 dark:text-gray-300 list-disc list-inside space-y-1 font-roboto">
                    <li>Browse and Sort Papers</li>
                    <li>View Original PDF and Markdown</li>
                    <li>Read Evaluation Results</li>
                    <li>View Detailed Scoring</li>
                    <li>See Raw JSON</li>
                  </ul>
                </div>
                <span ref={sourceArrowRef} className="group-hover:translate-x-1 transition-transform transition-colors duration-300" style={{ color: sourceArrowColor }}>→</span>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}
